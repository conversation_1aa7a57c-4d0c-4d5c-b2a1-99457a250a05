["tests/test_basic_setup.py::test_config_creation", "tests/test_basic_setup.py::test_logging_setup", "tests/test_basic_setup.py::test_main_window_creation", "tests/test_basic_setup.py::test_package_imports", "tests/test_hl7_parsing.py::TestHL7DataModels::test_component", "tests/test_hl7_parsing.py::TestHL7DataModels::test_field", "tests/test_hl7_parsing.py::TestHL7DataModels::test_message", "tests/test_hl7_parsing.py::TestHL7DataModels::test_segment", "tests/test_hl7_parsing.py::TestHL7DataModels::test_subcomponent", "tests/test_hl7_parsing.py::TestHL7MessageCollection::test_collection_creation", "tests/test_hl7_parsing.py::TestHL7MessageCollection::test_collection_operations", "tests/test_hl7_parsing.py::TestHL7Parser::test_encoding_detection", "tests/test_hl7_parsing.py::TestHL7Parser::test_parse_basic_message", "tests/test_hl7_parsing.py::TestHL7Parser::test_parse_invalid_message", "tests/test_hl7_parsing.py::TestHL7Parser::test_parse_multiple_messages", "tests/test_hl7_parsing.py::TestHL7Parser::test_parser_initialization", "tests/test_hl7_parsing.py::TestHL7Separators::test_custom_separators", "tests/test_hl7_parsing.py::TestHL7Separators::test_default_separators", "tests/test_hl7_parsing.py::TestHL7Separators::test_from_msh_segment", "tests/test_hl7_parsing.py::TestHL7Validator::test_segment_definitions", "tests/test_hl7_parsing.py::TestHL7Validator::test_validate_invalid_message", "tests/test_hl7_parsing.py::TestHL7Validator::test_validate_valid_message", "tests/test_hl7_parsing.py::TestHL7Validator::test_validator_initialization"]